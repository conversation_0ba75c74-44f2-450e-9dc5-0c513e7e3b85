<template>
  <div class="editor-preview h-full flex flex-col">
    <!-- 顶部工具栏 -->
    <div class="toolbar flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
      <!-- 左侧：编辑工具 -->
      <div class="flex items-center space-x-2">
        <button
          v-for="tool in toolbarItems"
          :key="tool.name"
          @click="tool.name === 'image' ? handleImageSelection() : insertMarkdown(tool.markdown)"
          :title="tool.name === 'image' ? '图片设置 (Ctrl+Shift+I)' : tool.title"
          class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded"
        >
          <span v-html="tool.icon"></span>
        </button>
      </div>

      <!-- 中间：视图切换 -->
      <div class="flex items-center space-x-1 bg-white border border-gray-300 rounded-lg p-1">
        <button
          @click="setViewMode('edit')"
          :class="[
            'px-3 py-1 text-sm rounded transition-colors',
            viewMode === 'edit' 
              ? 'bg-primary-500 text-white' 
              : 'text-gray-600 hover:text-gray-900'
          ]"
        >
          编辑
        </button>
        <button
          @click="setViewMode('split')"
          :class="[
            'px-3 py-1 text-sm rounded transition-colors',
            viewMode === 'split' 
              ? 'bg-primary-500 text-white' 
              : 'text-gray-600 hover:text-gray-900'
          ]"
        >
          分屏
        </button>
        <button
          @click="setViewMode('preview')"
          :class="[
            'px-3 py-1 text-sm rounded transition-colors',
            viewMode === 'preview' 
              ? 'bg-primary-500 text-white' 
              : 'text-gray-600 hover:text-gray-900'
          ]"
        >
          预览
        </button>
      </div>

      <!-- 右侧：预览控制 -->
      <div class="flex items-center space-x-2">
        <span class="text-sm text-gray-600">PDF预览</span>

        <button
          @click="refreshPreview"
          :disabled="isLoading || viewMode === 'edit'"
          class="px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded hover:bg-primary-200 disabled:opacity-50"
        >
          {{ isLoading ? '生成中...' : '刷新' }}
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area flex-1 flex overflow-hidden">
      <!-- 编辑器区域 -->
      <div 
        :class="[
          'editor-panel transition-all duration-300',
          viewMode === 'edit' ? 'w-full' : 
          viewMode === 'split' ? 'w-1/2' : 'w-0 overflow-hidden'
        ]"
      >
        <div class="h-full flex flex-col">
          <!-- 编辑器 -->
          <div class="flex-1 overflow-hidden">
            <textarea
              ref="textareaRef"
              v-model="content"
              @input="handleInput"
              @keydown="handleKeydown"
              @scroll="handleScroll"
              class="w-full h-full p-4 border-none outline-none resize-none font-mono text-sm leading-relaxed"
              placeholder="在此输入Markdown内容..."
            ></textarea>
          </div>
          
          <!-- 编辑器状态栏 -->
          <div class="status-bar flex items-center justify-between px-4 py-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500">
            <div class="flex items-center space-x-4">
              <span>行: {{ currentLine }}</span>
              <span>列: {{ currentColumn }}</span>
              <span>字符: {{ content.length }}</span>
              <span>字数: {{ wordCount }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span>Markdown</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div 
        v-if="viewMode === 'split'" 
        class="w-px bg-gray-300 flex-shrink-0"
      ></div>

      <!-- 预览区域 -->
      <div 
        :class="[
          'preview-panel transition-all duration-300',
          viewMode === 'preview' ? 'w-full' : 
          viewMode === 'split' ? 'w-1/2' : 'w-0 overflow-hidden'
        ]"
      >
        <div class="h-full flex flex-col">
          <!-- 预览内容 -->
          <div class="flex-1 overflow-auto bg-gray-100">
            <!-- 加载状态 -->
            <div v-if="isLoading" class="flex items-center justify-center h-full">
              <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
                <p class="text-gray-600">正在生成预览...</p>
              </div>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="error" class="flex items-center justify-center h-full">
              <div class="text-center">
                <div class="text-red-500 text-4xl mb-4">⚠️</div>
                <p class="text-red-600 mb-4">{{ error }}</p>
                <button @click="refreshPreview" class="btn-primary">
                  重试
                </button>
              </div>
            </div>

            <!-- PDF预览 -->
            <div
              v-else-if="pdfPreview"
              class="pdf-preview-container p-4"
            >
              <iframe
                :src="pdfPreview"
                class="w-full h-full border-0 bg-white shadow-lg mx-auto"
                style="min-height: 600px;"
              ></iframe>
            </div>

            <!-- 空状态 -->
            <div v-else class="flex items-center justify-center h-full">
              <div class="text-center text-gray-500">
                <div class="text-4xl mb-4">📄</div>
                <p>输入内容后将显示预览</p>
              </div>
            </div>
          </div>

          <!-- 预览状态栏 -->
          <div v-if="previewInfo" class="preview-info p-3 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between text-xs text-gray-600">
              <div class="flex items-center space-x-4">
                <span>页面: {{ previewInfo.pageFormat }}</span>
                <span>字体: {{ previewInfo.fontSize }}pt</span>
                <span>行高: {{ previewInfo.lineHeight }}</span>
              </div>
              <div class="flex items-center space-x-4">
                <span>预计页数: {{ estimatedPages }}</span>
                <span>字数: {{ wordCount }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片控制组件 -->
    <ImageControl
      :selected-image-text="selectedImageText"
      :cursor-position="cursorPosition"
      @image-updated="handleImageUpdate"
      @panel-closed="handleImagePanelClosed"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { pdfAPI } from '@/utils/api'
import ImageControl from './ImageControl.vue'
import type { LayoutConfig } from '@/types/layout'

// 组件属性
interface Props {
  modelValue: string
  config: LayoutConfig
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

// 响应式数据
const content = ref(props.modelValue)
const viewMode = ref<'edit' | 'split' | 'preview'>('split')
const isLoading = ref(false)
const error = ref('')
const pdfPreview = ref('')
const textareaRef = ref<HTMLTextAreaElement>()
const currentLine = ref(1)
const currentColumn = ref(1)

// 图片控制相关
const selectedImageText = ref('')
const cursorPosition = ref({ x: 0, y: 0 })

// 工具栏配置
const toolbarItems = [
  {
    name: 'bold',
    title: '粗体',
    icon: '<strong>B</strong>',
    markdown: '**粗体文本**'
  },
  {
    name: 'italic',
    title: '斜体',
    icon: '<em>I</em>',
    markdown: '*斜体文本*'
  },
  {
    name: 'heading',
    title: '标题',
    icon: 'H1',
    markdown: '# 标题'
  },
  {
    name: 'link',
    title: '链接',
    icon: '🔗',
    markdown: '[链接文本](URL)'
  },
  {
    name: 'image',
    title: '图片',
    icon: '🖼️',
    markdown: '![图片描述](图片URL)'
  },
  {
    name: 'code',
    title: '代码',
    icon: '</>',
    markdown: '`代码`'
  },
  {
    name: 'codeblock',
    title: '代码块',
    icon: '{ }',
    markdown: '```\n代码块\n```'
  },
  {
    name: 'list',
    title: '列表',
    icon: '•',
    markdown: '- 列表项'
  },
  {
    name: 'table',
    title: '表格',
    icon: '⊞',
    markdown: '| 列1 | 列2 |\n|-----|-----|\n| 内容1 | 内容2 |'
  }
]

// 计算属性
const wordCount = computed(() => {
  const chinese = (content.value.match(/[\u4e00-\u9fff]/g) || []).length
  const english = (content.value.match(/[a-zA-Z]+/g) || []).join('').length
  return chinese + Math.ceil(english / 4)
})

const previewInfo = computed(() => {
  if (!props.config) return null
  
  return {
    pageFormat: props.config.page_format,
    fontSize: props.config.font_size,
    lineHeight: props.config.line_height
  }
})

const estimatedPages = computed(() => {
  if (!content.value || !props.config) return 1

  const wordsPerPage = 500
  return Math.max(1, Math.ceil(wordCount.value / wordsPerPage))
})

// 监听内容变化
watch(content, (newValue) => {
  emit('update:modelValue', newValue)
})

watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value) {
    content.value = newValue
  }
})

// 监听内容和配置变化，自动刷新预览
watch([() => content.value, () => props.config], () => {
  if (viewMode.value !== 'edit') {
    clearTimeout(refreshPreview.timeout)
    refreshPreview.timeout = setTimeout(() => {
      if (content.value && content.value.trim()) {
        refreshPreview()
      } else {
        pdfPreview.value = ''
        error.value = ''
      }
    }, 1000)
  }
}, { deep: true })

// 方法
const setViewMode = (mode: 'edit' | 'split' | 'preview') => {
  viewMode.value = mode

  // 切换到预览模式时，如果有内容就刷新预览
  if (mode !== 'edit' && content.value && content.value.trim()) {
    nextTick(() => {
      refreshPreview()
    })
  }

  // 切换到编辑模式时，聚焦编辑器
  if (mode === 'edit' || mode === 'split') {
    nextTick(() => {
      focusEditor()
    })
  }
}

const handleInput = () => {
  updateCursorPosition()
}

const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Tab') {
    e.preventDefault()
    insertAtCursor('  ')
  }

  if (e.ctrlKey && e.key === 'b') {
    e.preventDefault()
    insertMarkdown('**粗体文本**')
  }

  if (e.ctrlKey && e.key === 'i') {
    e.preventDefault()
    insertMarkdown('*斜体文本*')
  }

  // Ctrl+Shift+I 打开图片控制面板
  if (e.ctrlKey && e.shiftKey && e.key === 'I') {
    e.preventDefault()
    handleImageSelection()
  }
}

const handleScroll = () => {
  updateCursorPosition()
}

const insertMarkdown = (markdown: string) => {
  insertAtCursor(markdown)
  focusEditor()
}

const insertAtCursor = (text: string) => {
  const textarea = textareaRef.value
  if (!textarea) return

  const start = textarea.selectionStart
  const end = textarea.selectionEnd

  content.value = content.value.substring(0, start) + text + content.value.substring(end)

  nextTick(() => {
    textarea.focus()
    textarea.setSelectionRange(start + text.length, start + text.length)
    updateCursorPosition()
  })
}

const updateCursorPosition = () => {
  const textarea = textareaRef.value
  if (!textarea) return

  const cursorPos = textarea.selectionStart
  const textBeforeCursor = content.value.substring(0, cursorPos)
  const lines = textBeforeCursor.split('\n')

  currentLine.value = lines.length
  currentColumn.value = lines[lines.length - 1].length + 1
}

const focusEditor = () => {
  nextTick(() => {
    textareaRef.value?.focus()
  })
}

const refreshPreview = async () => {
  if (!content.value || !content.value.trim()) {
    pdfPreview.value = ''
    error.value = ''
    return
  }

  isLoading.value = true
  error.value = ''

  try {
    await generatePDFPreview()
  } catch (err: any) {
    console.error('预览生成失败:', err)
    error.value = err.response?.data?.detail || err.message || '预览生成失败'
  } finally {
    isLoading.value = false
  }
}

const generatePDFPreview = async () => {
  try {
    const response = await pdfAPI.preview({
      content: content.value,
      layout_config: props.config
    })

    if (response.success && response.pdf_data) {
      const pdfBlob = new Blob(
        [Uint8Array.from(atob(response.pdf_data), c => c.charCodeAt(0))],
        { type: 'application/pdf' }
      )
      pdfPreview.value = URL.createObjectURL(pdfBlob)
    } else {
      throw new Error('PDF预览生成失败')
    }
  } catch (error) {
    throw error
  }
}

// 图片控制相关方法
const handleImageSelection = () => {
  const textarea = textareaRef.value
  if (!textarea) return

  const cursorPos = textarea.selectionStart
  const textBeforeCursor = content.value.substring(0, cursorPos)
  const textAfterCursor = content.value.substring(cursorPos)

  // 查找当前行的图片语法
  const currentLineStart = textBeforeCursor.lastIndexOf('\n') + 1
  const currentLineEnd = textAfterCursor.indexOf('\n')
  const currentLineEndPos = currentLineEnd === -1 ? content.value.length : cursorPos + currentLineEnd

  const currentLine = content.value.substring(currentLineStart, currentLineEndPos)

  // 检查当前行是否包含图片语法
  const imageMatch = currentLine.match(/!\[(.*?)\]\((.*?)\)/)
  if (imageMatch) {
    selectedImageText.value = imageMatch[0]
    updateCursorScreenPosition()
  } else {
    // 如果当前行没有图片，查找最近的图片
    findNearestImage(cursorPos)
  }
}

const findNearestImage = (cursorPos: number) => {
  const imageRegex = /!\[(.*?)\]\((.*?)\)/g
  let match
  let nearestImage = ''
  let minDistance = Infinity

  while ((match = imageRegex.exec(content.value)) !== null) {
    const imageStart = match.index
    const imageEnd = imageStart + match[0].length

    // 计算距离光标的距离
    const distance = Math.min(
      Math.abs(cursorPos - imageStart),
      Math.abs(cursorPos - imageEnd)
    )

    if (distance < minDistance) {
      minDistance = distance
      nearestImage = match[0]
    }
  }

  if (nearestImage && minDistance < 200) { // 200字符内的图片
    selectedImageText.value = nearestImage
    updateCursorScreenPosition()
  }
}

const updateCursorScreenPosition = () => {
  const textarea = textareaRef.value
  if (!textarea) return

  const rect = textarea.getBoundingClientRect()
  cursorPosition.value = {
    x: rect.left + 20,
    y: rect.top + 20
  }
}

const handleImageUpdate = (newImageMarkdown: string) => {
  if (!selectedImageText.value) return

  // 替换选中的图片文本
  const newContent = content.value.replace(selectedImageText.value, newImageMarkdown)
  content.value = newContent

  // 清空选中状态
  selectedImageText.value = ''
}

const handleImagePanelClosed = () => {
  selectedImageText.value = ''
}

// 暴露方法给父组件
defineExpose({
  focus: focusEditor,
  insertText: insertAtCursor,
  setViewMode,
  refreshPreview
})
</script>

<style scoped>
.editor-preview {
  @apply border border-gray-300 rounded-lg overflow-hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-area {
  flex: 1;
  min-height: 0; /* 重要：允许flex子元素收缩 */
}

.editor-panel {
  height: 100%;
  overflow: hidden;
}

.preview-panel {
  height: 100%;
  /* 不设置overflow: hidden，允许内部元素控制滚动 */
}

.editor-panel textarea {
  height: 100%;
  box-sizing: border-box;
}

.preview-panel > div {
  height: 100%;
}

.toolbar button {
  @apply flex items-center justify-center w-8 h-8;
}

.editor-panel textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}



.pdf-preview-container {
  @apply flex justify-center;
  min-height: 100%;
  overflow-y: auto;
  height: 100%;
  scroll-behavior: smooth; /* 平滑滚动 */
}


</style>
