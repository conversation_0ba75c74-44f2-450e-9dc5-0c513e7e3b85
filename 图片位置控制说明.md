# 图片位置精细控制说明

本文档详细介绍如何在PDF生成系统中精细控制图片的位置。

## 基本语法

图片的基本语法为：
```
![图片说明](图片路径?参数1=值1&参数2=值2)
```

## 支持的参数类型

### 1. 基本尺寸控制

| 参数 | 说明 | 示例 | 效果 |
|------|------|------|------|
| `size=small` | 小尺寸（40%宽度） | `?size=small` | 适合并排显示 |
| `size=medium` | 中等尺寸（70%宽度） | `?size=medium` | 标准显示 |
| `size=large` | 大尺寸（90%宽度） | `?size=large` | 突出显示 |
| `size=original` | 原始尺寸 | `?size=original` | 保持原图大小 |
| `width=数值` | 指定宽度（像素） | `?width=200` | 精确宽度控制 |
| `height=数值` | 指定高度（像素） | `?height=150` | 精确高度控制 |

### 2. 对齐控制

| 参数 | 说明 | 示例 | 效果 |
|------|------|------|------|
| `align=left` | 左对齐 | `?align=left` | 图片靠左显示 |
| `align=center` | 居中对齐（默认） | `?align=center` | 图片居中显示 |
| `align=right` | 右对齐 | `?align=right` | 图片靠右显示 |

### 3. 边距控制

| 参数 | 说明 | 示例 | 效果 |
|------|------|------|------|
| `margin_left=数值` | 左边距（像素） | `?margin_left=20` | 图片左侧增加空白 |
| `margin_right=数值` | 右边距（像素） | `?margin_right=20` | 图片右侧增加空白 |
| `margin_top=数值` | 上边距（像素） | `?margin_top=10` | 图片上方增加空白 |
| `margin_bottom=数值` | 下边距（像素） | `?margin_bottom=10` | 图片下方增加空白 |

### 4. 偏移控制

| 参数 | 说明 | 示例 | 效果 |
|------|------|------|------|
| `offset_x=数值` | 水平偏移（像素） | `?offset_x=15` | 正值向右，负值向左 |
| `offset_y=数值` | 垂直偏移（像素） | `?offset_y=10` | 正值向下，负值向上 |

### 5. 浮动控制（未来功能）

| 参数 | 说明 | 示例 | 效果 |
|------|------|------|------|
| `float=left` | 左浮动 | `?float=left` | 文字环绕右侧 |
| `float=right` | 右浮动 | `?float=right` | 文字环绕左侧 |
| `float=none` | 不浮动（默认） | `?float=none` | 正常布局 |

## 实际应用示例

### 示例1：基本对齐
```markdown
![左对齐图片](image.png?size=small&align=left)
![居中图片](image.png?size=medium&align=center)
![右对齐图片](image.png?size=small&align=right)
```

### 示例2：边距控制
```markdown
![带边距的图片](image.png?size=medium&margin_left=30&margin_right=30)
```

### 示例3：精确位置控制
```markdown
![精确位置图片](image.png?width=200&height=150&offset_x=20&offset_y=10)
```

### 示例4：复杂组合
```markdown
![复杂布局图片](image.png?size=medium&align=left&margin_right=40&offset_y=5)
```

## 参数组合规则

1. **尺寸参数优先级**：`width/height` > `size` > 默认
2. **边距和偏移可以同时使用**：边距影响容器大小，偏移影响图片在容器内的位置
3. **对齐参数影响整体布局**：在应用边距和偏移之前确定基本对齐方式

## 使用建议

### 1. 图片并排显示
```markdown
![图片1](image1.png?size=small&margin_right=10)
![图片2](image2.png?size=small&margin_left=10)
```

### 2. 图文混排
```markdown
![左侧图片](image.png?size=small&align=left&margin_right=20)

这段文字会出现在图片右侧，形成图文混排的效果。

![右侧图片](image.png?size=small&align=right&margin_left=20)
```

### 3. 精确定位
```markdown
![标题图片](logo.png?width=100&align=center&margin_bottom=20)
![内容图片](content.png?size=large&offset_y=10)
```

## 注意事项

1. **像素值**：所有数值参数都以像素为单位
2. **页面边界**：图片不会超出页面边界，系统会自动调整
3. **性能考虑**：复杂的位置控制可能会影响PDF生成速度
4. **兼容性**：建议先使用基本参数，再逐步添加高级控制

## 调试技巧

1. **从简单开始**：先使用基本的`size`和`align`参数
2. **逐步添加**：一次添加一个参数，观察效果
3. **测试不同尺寸**：在不同的图片尺寸下测试参数效果
4. **查看生成的PDF**：最终效果以PDF为准

通过这些精细的位置控制参数，您可以创建出专业、美观的PDF文档布局！
