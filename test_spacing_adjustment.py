#!/usr/bin/env python3
"""
测试序号与文字间距调整效果
"""

import asyncio
import os
import sys

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_spacing_adjustment():
    """测试序号与文字间距调整效果"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容 - 重点测试间距效果
    test_content = """# 序号与文字间距调整测试

## 调整说明

之前序号与文字距离过远（15px），现在调整为8px，使排版更紧凑美观。

## 基本编号列表测试

1. 第一个列表项测试间距效果
2. 第二个列表项包含（（楷体双括号内容））
3. 第三个列表项：**粗体**、（（楷体））、*斜体*混合格式

## 不同长度文本测试

1. 短文本
2. 中等长度的文本内容测试
3. 这是一个比较长的列表项文本，用来测试在较长内容情况下序号与文字的间距是否合适
4. 超长文本测试：这是一个很长很长的列表项内容，包含了大量的文字信息，用来验证在文本换行的情况下，序号与文字开头的对齐和间距效果是否理想

## 特殊内容测试

1. 包含数字：123456789
2. 包含英文：English Content Test
3. 包含符号：！@#￥%……&*（）
4. 包含楷体：（（重要楷体强调内容））

## 实际应用场景

### 数学题目
1. 计算下列各题：（（注意运算顺序））
2. 解方程：x + 5 = 12
3. 应用题：小明有（（关键信息））苹果

### 语文阅读
1. 阅读理解：理解（（中心思想））
2. 词语解释：解释（（重点词汇））的含义
3. 写作要求：注意（（写作技巧））

### 英语练习
1. Grammar: Learn the （（grammar rules））
2. Vocabulary: Remember （（key words））
3. Speaking: Practice （（pronunciation））

## 多级编号测试

1. 一级列表项
2. 另一个一级项目
3. 第三个一级项目
4. 第四个一级项目
5. 第五个一级项目
6. 第六个一级项目
7. 第七个一级项目
8. 第八个一级项目
9. 第九个一级项目
10. 第十个一级项目（测试两位数）

## 间距对比验证

调整前：序号圆形右侧15px开始文字
调整后：序号圆形右侧8px开始文字

预期效果：
- 序号与文字更紧密
- 整体排版更紧凑
- 视觉效果更协调
- 不影响可读性

## 总结

（（如果间距调整合适，整个编号列表应该看起来更加紧凑和美观！））
"""
    
    # 配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_family="Noto Sans CJK SC",
        font_size=12,
        line_height=1.5,
        paragraph_spacing=6,
        indent_first_line=True,
        dpi=300,
        color_mode="CMYK",
        bleed=3,
        enable_hyphenation=True,
        widow_orphan_control=True
    )
    
    try:
        # 生成PDF
        pdf_path = await pdf_service.generate_pdf(
            test_content, 
            config, 
            "spacing_adjustment_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print(f"📄 文件位置: {os.path.abspath(pdf_path)}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📊 文件大小: {file_size} 字节")
            
            print("\n📏 间距调整详情:")
            print("   📐 调整前：圆形右侧15px开始文字")
            print("   📐 调整后：圆形右侧8px开始文字")
            print("   📉 间距减少：7px（约47%减少）")
            print("   🎯 效果：更紧凑的排版")
            
            print("\n🔧 技术参数:")
            print("   🔵 圆形直径：20px")
            print("   📍 圆形位置：距左边10px")
            print("   📝 文字开始：距左边28px（10+10+8）")
            print("   📏 总体间距：更加合理")
            
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_spacing_adjustment())
