# 重难点剖析功能说明

## 🎯 功能概述

"重难点剖析"功能与"答案"功能完全相同，可以将文档中的"重难点剖析"文字自动替换为精美的图片标签，提升文档的视觉效果和专业性。

## 📐 图片规格

### 基本信息
- **原始尺寸：** 209 × 101 像素
- **显示尺寸：** 40 × 19.3 像素（与答案图片相同）
- **缩放比例：** 0.19（缩小到原始尺寸的19%）
- **宽高比：** 2.07:1

### 视觉设计
- **背景颜色：** 橙色 (#f7ab00)
- **文字颜色：** 白色
- **边框：** 稍深的橙色边框
- **圆角：** 15像素圆角矩形
- **字体：** 系统中文字体，24pt

## 📍 位置和对齐

### 水平位置
- **X坐标：** -10像素（负值）
- **效果：** 突出到答案框左边外面
- **对齐方式：** 比答案框本身更靠左

### 垂直位置
- **Y坐标：** 根据内容流动态计算
- **间距：** 图片下方5像素间距

## 🎨 图片版本

系统提供了三个版本的重难点剖析图片：

| 文件名 | 用途 | 位置 |
|--------|------|------|
| `key_point_label.png` | 标准版本 | `answer_images/` |
| `key_point_label_small.png` | 小尺寸版本 | `answer_images/` |
| `key_point_label_large.png` | 大尺寸版本 | `answer_images/` |

**注意：** 系统会自动选择第一个找到的版本。

## 📝 使用方法

### 基本语法
在答案框（`/` 和 `/` 之间）中直接使用"重难点剖析"文字：

```markdown
/
这是一个数学题的解析。

重难点剖析：这里是重点分析内容。

计算过程：...

答案：结果
/
```

### 混合使用
可以与答案图片混合使用：

```markdown
/
重难点剖析：解题的关键步骤
1. 理解题意
2. 分析数量关系
3. 列式计算

计算过程：...

答案：最终结果
/
```

### 多个使用
可以在同一个答案框中使用多个重难点剖析：

```markdown
/
重难点剖析：概念理解
- 基本概念解释
- 关键点分析

计算步骤：...

重难点剖析：易错点提醒
- 常见错误
- 注意事项

答案：正确结果
/
```

## 🔧 技术实现

### 处理流程
1. **文本扫描：** 系统扫描答案框中的"重难点剖析"文字
2. **图片替换：** 将文字替换为对应的图片
3. **尺寸计算：** 按照预设规则计算图片显示尺寸
4. **位置定位：** 将图片定位到答案框外面
5. **渲染输出：** 在PDF中绘制图片

### 核心代码
```python
def _create_key_point_image(self, max_width: float) -> Optional[Image]:
    """创建重难点剖析标签图片"""
    # 计算合适的尺寸（与答案图片相同）
    target_width = min(40, max_width * 0.1)  # 最大40像素或10%宽度
    scale_ratio = min(target_width / orig_width, 1.0)  # 不放大
    
    new_width = orig_width * scale_ratio
    new_height = orig_height * scale_ratio
    
    # 创建ReportLab Image对象
    img = Image(key_point_image_path, width=new_width, height=new_height)
    return img
```

### 绘制位置
```python
# 重难点剖析图片左对齐显示（突出到答案框外面）
img_x = -10  # 负值让图片突出到答案框左边外面10px
content_obj.drawOn(canvas, img_x, current_y)
```

## 🎯 设计特点

### 与答案图片的一致性
- **相同尺寸：** 40×19.3像素
- **相同位置：** 突出到答案框外面
- **相同处理：** 完全相同的替换逻辑
- **相同样式：** 橙色背景，白色文字

### 视觉效果
- **醒目突出：** 橙色背景在文档中非常醒目
- **专业美观：** 圆角矩形设计，视觉效果佳
- **尺寸适中：** 不会过大影响阅读，也不会太小看不清
- **位置合理：** 突出显示但不影响正文布局

## 📋 使用场景

### 教育文档
- **知识点分析：** 重难点剖析：函数的单调性
- **解题思路：** 重难点剖析：解方程的关键步骤
- **概念解释：** 重难点剖析：平均数的含义

### 技术文档
- **核心要点：** 重难点剖析：算法的时间复杂度
- **关键步骤：** 重难点剖析：数据库设计原则
- **注意事项：** 重难点剖析：并发编程的陷阱

### 培训材料
- **重点内容：** 重难点剖析：销售技巧的核心
- **难点解析：** 重难点剖析：财务报表分析方法
- **关键流程：** 重难点剖析：项目管理的关键环节

## ✅ 功能优势

1. **自动化处理：** 无需手动插入图片，系统自动替换
2. **视觉统一：** 与答案图片保持一致的设计风格
3. **灵活使用：** 可以单独使用或与答案混合使用
4. **专业美观：** 提升文档的专业性和视觉效果
5. **易于维护：** 统一的图片管理和处理逻辑

## 🔄 与答案功能的对比

| 特性 | 答案功能 | 重难点剖析功能 |
|------|----------|----------------|
| **触发词** | "答案" | "重难点剖析" |
| **图片尺寸** | 40×19.3像素 | 40×19.3像素 |
| **位置** | 突出到答案框外面 | 突出到答案框外面 |
| **样式** | 橙色背景，白色文字 | 橙色背景，白色文字 |
| **使用场景** | 标记答案内容 | 标记重难点分析 |
| **处理逻辑** | 完全相同 | 完全相同 |

## 🎉 总结

重难点剖析功能为文档提供了更丰富的标记选项，与答案功能形成完美的配对，让教育和培训文档更加专业和美观。通过统一的设计风格和处理逻辑，确保了功能的一致性和易用性。
