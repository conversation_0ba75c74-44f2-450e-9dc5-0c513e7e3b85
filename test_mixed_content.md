# 图片精细位置控制测试

这是一个测试文档，用于演示图片的精细位置控制功能。

## 基本对齐控制

### 左对齐
![左对齐图片](test_images/calculation_steps.png?size=small&align=left)

### 居中对齐（默认）
![居中图片](test_images/math_diagram.png?size=small&align=center)

### 右对齐
![右对齐图片](test_images/rectangle.png?size=small&align=right)

## 边距控制

### 左边距
![左边距图片](test_images/calculation_steps.png?size=small&margin_left=50)

### 右边距
![右边距图片](test_images/math_diagram.png?size=small&margin_right=50)

### 上下边距
![上下边距图片](test_images/rectangle.png?size=small&margin_top=20&margin_bottom=20)

## 偏移控制

### 水平偏移
![水平偏移图片](test_images/calculation_steps.png?size=small&offset_x=30)

### 垂直偏移
![垂直偏移图片](test_images/math_diagram.png?size=small&offset_y=15)

### 组合偏移
![组合偏移图片](test_images/rectangle.png?size=small&offset_x=20&offset_y=10)

## 复杂组合控制

### 左对齐 + 右边距 + 垂直偏移
![复杂控制1](test_images/calculation_steps.png?size=medium&align=left&margin_right=30&offset_y=5)

### 右对齐 + 左边距 + 水平偏移
![复杂控制2](test_images/math_diagram.png?size=medium&align=right&margin_left=30&offset_x=-10)

## 图片与文字混合布局

![计算步骤图](test_images/calculation_steps.png?size=small&margin_bottom=10)

这是穿插在两张图片之间的文字内容。这段文字会出现在第一张图片和第二张图片之间，形成图文混排的效果。

![数学图表](test_images/math_diagram.png?size=small&margin_top=10)

这是第二张图片后面的文字说明。

## 更复杂的混合布局

![左对齐图片](test_images/calculation_steps.png?size=small&align=left)

这是左对齐图片后的文字说明。

![居中图片](test_images/math_diagram.png?size=small&align=center)

这是居中图片后的文字。

![右对齐图片](test_images/rectangle.png?size=small&align=right)

这是右对齐图片后的文字。

## 三张图片与文字混合

![计算步骤](test_images/calculation_steps.png?size=small)

第一段穿插文字：这里是第一张图片的说明文字。

![数学图表](test_images/math_diagram.png?size=small)

第二段穿插文字：这里是第二张图片的说明文字，可以包含**粗体**和*斜体*格式。

![矩形图](test_images/rectangle.png?size=small)

第三段穿插文字：这里是第三张图片的说明文字，还可以包含（（特殊格式的文字））。

## 答案框中的图片与文字混合

/
答案：这是答案内容

![答案图片1](test_images/calculation_steps.png?size=small)

这是答案图片的说明文字。

![答案图片2](test_images/math_diagram.png?size=small)

解析：这是解析内容，包含了图片和文字的混合布局。
/

## 普通段落

这是一个普通段落，用于分隔不同的测试内容。

## 编号列表

1. 第一个列表项
2. 第二个列表项
3. 第三个列表项

## 结束

测试文档结束。
