#!/usr/bin/env python3
"""
答案框间距优化测试
测试答案框下方空白间距的改进效果
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_answer_box_spacing():
    """测试答案框间距优化"""
    
    pdf_service = PDFService()
    
    test_content = """
# 答案框间距优化测试

## 第一题：基础测试

这是一个简单的计算题。

/
答案：42

这道题的答案很容易得出。
/

## 第二题：多段落测试

/
答案：这是第一个答案

解析：这是详细的解析内容，用来测试多段落在答案框中的显示效果。

答案：这是第二个答案

更多解析内容，测试答案框的高度是否合适。
/

## 第三题：短内容测试

/
答案：简短答案
/

## 第四题：长内容测试

/
答案：这是一个比较长的答案，用来测试答案框在包含较多内容时的显示效果。

解析：这是详细的解析过程：
1. 第一步：分析题目条件
2. 第二步：建立数学模型
3. 第三步：求解方程
4. 第四步：验证答案

通过以上步骤，我们可以得出正确的答案。这个解析过程展示了完整的解题思路。

答案：最终答案是正确的，符合题目要求。
/

## 第五题：混合格式测试

/
**重要提示：** 这道题需要特别注意

答案：混合格式答案

**解题步骤：**
- 步骤一：理解题意
- 步骤二：列出已知条件
- 步骤三：应用相关公式

（（这是重要的注意事项））

答案：通过以上分析得出的最终答案
/

## 间距对比说明

通过以上测试，我们可以观察到：

1. **优化前**：答案框下方有较多空白，显得不够紧凑
2. **优化后**：答案框下方空白减少，整体更加紧凑美观
3. **内边距调整**：从15px减少到8px，底部内边距进一步减半
4. **视觉效果**：答案框高度更贴合实际内容，减少不必要的空白

## 技术改进点

### 内边距优化
- **顶部内边距**：保持8px，确保内容不贴边
- **底部内边距**：减少到4px，减少下方空白
- **左右内边距**：保持8px，确保左右对称

### 高度计算优化
- **精确计算**：根据实际内容高度计算答案框高度
- **动态调整**：底部内边距根据内容自动调整
- **紧凑布局**：减少不必要的空白区域

这些优化让答案框看起来更加专业和美观。
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.5,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("生成答案框间距优化测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="answer_box_spacing_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("\n📋 测试要点:")
        print("1. 检查答案框下方的空白是否减少")
        print("2. 确认答案框高度更贴合内容")
        print("3. 验证整体布局是否更加紧凑")
        print("4. 对比不同内容长度的答案框效果")
        
        return pdf_path
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_answer_box_spacing())
