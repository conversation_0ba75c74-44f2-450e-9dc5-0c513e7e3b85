#!/usr/bin/env python3
"""
最终测试重难点剖析功能
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_final_key_point():
    """最终测试重难点剖析功能"""
    
    print("🎯 最终测试重难点剖析功能...")
    
    # 创建PDF服务
    pdf_service = PDFService()
    
    # 完整的测试内容
    test_content = """
# 重难点剖析功能完整测试

## 第一题：混合使用测试

解方程：2x + 6 = 14

/
这是一个一元一次方程。

重难点剖析：解一元一次方程的关键步骤
1. 移项：将含x的项移到等号左边，常数项移到右边
2. 合并同类项
3. 系数化为1

**解题过程：**
2x + 6 = 14
2x = 14 - 6
2x = 8
x = 4

**验证：** 2 × 4 + 6 = 8 + 6 = 14 ✓

答案：x = 4

重难点剖析：验证的重要性
解完方程后一定要验证，确保答案正确。
/

## 第二题：只有重难点剖析

计算三角形面积：底边 = 8cm，高 = 5cm

/
这是一个几何计算题。

重难点剖析：三角形面积公式的理解
- 公式：S = ½ × 底 × 高
- 关键点：为什么要乘以½？
- 原理：三角形是平行四边形的一半

**计算过程：**
S = ½ × 8 × 5 = ½ × 40 = 20平方厘米

重难点剖析：单位换算
- 面积单位：平方厘米（cm²）
- 注意：长度单位是厘米，面积单位是平方厘米
/

## 第三题：只有答案

计算：15 × 4 = ?

/
这是一个简单的乘法题目。

**计算过程：**
15 × 4 = 60

答案：60
/

## 第四题：复杂混合使用

小明有50元，买了3支笔，每支8元，还剩多少钱？

/
这是一个综合应用题。

重难点剖析：应用题解题步骤
1. **理解题意**：明确已知条件和求解目标
2. **分析数量关系**：找出各个量之间的关系
3. **列式计算**：根据数量关系列出算式
4. **检验答案**：验证答案是否合理

**已知条件：**
- 小明有50元
- 买了3支笔，每支8元

**数量关系分析：**
- 买笔花费 = 笔的数量 × 每支笔的价格
- 剩余钱数 = 原有钱数 - 买笔花费

**计算过程：**
买笔花费：3 × 8 = 24元
剩余钱数：50 - 24 = 26元

重难点剖析：检验方法
- 估算验证：3×8≈24，50-24=26，合理
- 逆向验证：26+24=50，符合原有钱数

答案：26元

这道题通过重难点剖析，帮助学生掌握应用题的解题方法和思维过程。
/
"""
    
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=3.0,  # 保持较大左边距以容纳突出的图片
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("生成最终重难点剖析测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_final_key_point.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
                
                print("\n🎉 重难点剖析功能测试完成！")
                print("\n请检查PDF中的效果:")
                print("1. ✅ '重难点剖析'文字应该被替换为橙色图片")
                print("2. ✅ 重难点剖析图片应该与答案图片大小相同")
                print("3. ✅ 重难点剖析图片应该突出到答案框外面")
                print("4. ✅ 可以与答案图片在同一个答案框中混合使用")
                print("5. ✅ 多个重难点剖析图片都正常显示")
                
                print("\n🎯 功能特点:")
                print("- 图片尺寸：40×19.3像素")
                print("- 背景颜色：橙色 (#f7ab00)")
                print("- 文字颜色：白色")
                print("- 位置：突出到答案框外面")
                print("- 处理方式：与答案图片完全相同")
                
                print("\n📝 使用方法:")
                print("在答案框中直接使用'重难点剖析'文字，系统会自动替换为图片")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_final_key_point())
