#!/usr/bin/env python3
"""
测试文本处理逻辑
"""

def test_text_processing():
    """测试文本处理"""
    
    test_texts = [
        "重难点剖析：这是测试内容。",
        "答案：这是答案。",
        "重难点剖析：这是测试内容。\n\n答案：这是答案。",
        "这里有重难点剖析和答案的混合内容。"
    ]
    
    for i, text in enumerate(test_texts):
        print(f"\n测试 {i+1}: {text}")
        print(f"包含'重难点剖析': {'重难点剖析' in text}")
        print(f"包含'答案': {'答案' in text}")
        
        # 模拟处理逻辑
        if '重难点剖析' in text:
            print("✅ 会处理重难点剖析")
        if '答案' in text:
            print("✅ 会处理答案")

if __name__ == "__main__":
    test_text_processing()
