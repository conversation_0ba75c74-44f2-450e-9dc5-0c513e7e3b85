#!/usr/bin/env python3
"""
测试序号与文字的垂直对齐效果
"""

import asyncio
import os
import sys

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_vertical_alignment():
    """测试序号与文字的垂直对齐效果"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容 - 重点测试垂直对齐
    test_content = """# 序号与文字垂直对齐测试

## 调整说明

调整了序号圆形的垂直位置，使其与文字水平居中对齐：
- 圆形位置：稍微下移，与文本基线对齐
- 文字位置：段落中心与圆形中心对齐

## 基本对齐测试

1. 短文本对齐测试
2. 中等长度文本对齐测试效果
3. 这是一个较长的文本内容，用来测试在文字较多时序号与文字的垂直对齐效果
4. 包含（（楷体双括号内容））的对齐测试

## 不同字体大小对比

### 12号字体（当前）
1. 第一个测试项目
2. 第二个测试项目（（楷体内容））
3. 第三个测试项目：**粗体**和*斜体*

## 混合格式对齐测试

1. **粗体开头**的列表项对齐测试
2. *斜体开头*的列表项对齐测试
3. （（楷体开头））的列表项对齐测试
4. `代码开头`的列表项对齐测试

## 复杂格式对齐测试

1. 混合格式：**粗体**、（（楷体））、*斜体*、`代码`的组合对齐
2. 长文本混合：这是一个包含多种格式的长文本（（楷体强调内容））用来测试复杂情况下的对齐效果
3. 特殊字符：包含！@#￥%……&*（）等符号的对齐测试

## 实际应用场景

### 数学题目
1. 计算：（（重要公式））的应用
2. 解题：注意（（关键步骤））
3. 验证：检查（（计算结果））

### 语文阅读
1. 理解：掌握（（文章主旨））
2. 分析：识别（（修辞手法））
3. 总结：归纳（（重点内容））

### 英语学习
1. Grammar: Master（（tense rules））
2. Vocabulary: Learn（（key words））
3. Speaking: Practice（（pronunciation））

## 两位数序号对齐测试

8. 第八项对齐测试
9. 第九项对齐测试
10. 第十项（两位数）对齐测试
11. 第十一项（（楷体内容））对齐测试
12. 第十二项：**粗体**（（楷体））*斜体*对齐测试

## 长文本换行对齐测试

1. 这是一个很长很长的列表项内容，包含了大量的文字信息，用来测试在文本内容非常多需要换行的情况下，序号圆形与文字第一行的垂直对齐效果是否正确
2. 另一个长文本测试（（包含楷体双括号强调内容，用来验证在复杂格式和长文本组合的情况下，垂直对齐是否仍然保持正确））
3. 最后一个综合测试：**粗体开头**，然后是（（楷体强调部分）），接着是*斜体内容*，最后是`代码文字`，整个内容很长需要换行显示

## 验证要点

如果垂直对齐调整成功，应该看到：
- 序号圆形与文字第一行水平居中对齐
- 不同长度文本的对齐效果一致
- 混合格式不影响对齐效果
- 两位数序号对齐正常

（（垂直对齐优化完成，视觉效果更加协调！））
"""
    
    # 配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_family="Noto Sans CJK SC",
        font_size=12,
        line_height=1.5,
        paragraph_spacing=6,
        indent_first_line=True,
        dpi=300,
        color_mode="CMYK",
        bleed=3,
        enable_hyphenation=True,
        widow_orphan_control=True
    )
    
    try:
        # 生成PDF
        pdf_path = await pdf_service.generate_pdf(
            test_content, 
            config, 
            "vertical_alignment_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print(f"📄 文件位置: {os.path.abspath(pdf_path)}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📊 文件大小: {file_size} 字节")
            
            print("\n📐 垂直对齐调整:")
            print("   🔵 圆形位置：稍微下移，与文本基线对齐")
            print("   📝 文字位置：段落中心与圆形中心对齐")
            print("   📏 水平间距：圆形右侧8px开始文字")
            print("   📋 首行缩进：编号列表无缩进")
            
            print("\n🎯 预期效果:")
            print("   ✅ 序号与文字水平居中对齐")
            print("   ✅ 不同长度文本对齐一致")
            print("   ✅ 混合格式对齐正常")
            print("   ✅ 双括号楷体正常显示")
            
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_vertical_alignment())
