<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>文件上传测试</h1>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>点击选择文件或拖拽文件到此处</p>
        <p>支持 .md, .docx, .txt 格式</p>
    </div>
    
    <input type="file" id="fileInput" accept=".md,.docx,.txt" style="display: none;">
    
    <div id="result"></div>
    
    <script>
        const fileInput = document.getElementById('fileInput');
        const resultDiv = document.getElementById('result');
        
        fileInput.addEventListener('change', handleFileUpload);
        
        async function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            showResult('正在上传文件...', 'loading');
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch('http://localhost:8001/api/documents/upload', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('响应数据:', result);
                    
                    showResult(`
                        <h3>上传成功！</h3>
                        <p><strong>文件ID:</strong> ${result.file_id}</p>
                        <p><strong>文件名:</strong> ${result.filename}</p>
                        <p><strong>文件类型:</strong> ${result.file_type}</p>
                        <p><strong>文件大小:</strong> ${result.file_size} bytes</p>
                        <p><strong>消息:</strong> ${result.message}</p>
                        <p><strong>内容长度:</strong> ${result.markdown_content.length} 字符</p>
                        <details>
                            <summary>查看内容</summary>
                            <pre>${result.markdown_content}</pre>
                        </details>
                    `, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`上传失败: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                console.error('上传错误:', error);
                showResult(`上传失败: ${error.message}`, 'error');
            }
        }
        
        function showResult(message, type) {
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        // 拖拽支持
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = '';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = '';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileUpload({ target: { files: files } });
            }
        });
    </script>
</body>
</html>
