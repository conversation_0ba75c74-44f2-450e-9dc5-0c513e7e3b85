#!/usr/bin/env python3
"""
创建"重难点剖析"图片文件
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_key_point_images():
    """创建重难点剖析图片"""
    
    print("🎨 创建重难点剖析图片...")
    
    # 确保目录存在
    os.makedirs("answer_images", exist_ok=True)
    
    # 图片尺寸（与答案图片相同的比例）
    width = 209
    height = 101
    
    # 创建三个版本的重难点剖析图片
    versions = [
        ("key_point_label.png", "重难点剖析"),
        ("key_point_label_small.png", "重难点剖析"),
        ("key_point_label_large.png", "重难点剖析")
    ]
    
    for filename, text in versions:
        # 创建图片
        img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 背景颜色（使用橙色，与答案图片类似）
        bg_color = (247, 171, 0, 255)  # #f7ab00
        
        # 绘制圆角矩形背景
        corner_radius = 15
        draw.rounded_rectangle(
            [(0, 0), (width-1, height-1)],
            radius=corner_radius,
            fill=bg_color,
            outline=(200, 140, 0, 255),  # 稍深的边框
            width=2
        )
        
        # 尝试使用系统字体
        try:
            # macOS 中文字体
            font_paths = [
                "/System/Library/Fonts/STHeiti Light.ttc",
                "/System/Library/Fonts/Helvetica.ttc",
                "/Library/Fonts/Arial Unicode MS.ttf"
            ]
            
            font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        font = ImageFont.truetype(font_path, 24)
                        break
                    except:
                        continue
            
            if font is None:
                font = ImageFont.load_default()
                
        except:
            font = ImageFont.load_default()
        
        # 计算文字位置（居中）
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        # 绘制白色文字
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        
        # 保存图片
        filepath = os.path.join("answer_images", filename)
        img.save(filepath, "PNG")
        
        print(f"✅ 创建图片: {filepath}")
        print(f"   📐 尺寸: {width} × {height} 像素")
        print(f"   📝 文字: {text}")
    
    print("\n🎉 重难点剖析图片创建完成！")
    print("📁 图片位置: answer_images/")
    print("📋 创建的文件:")
    print("   - key_point_label.png (标准版本)")
    print("   - key_point_label_small.png (小尺寸版本)")
    print("   - key_point_label_large.png (大尺寸版本)")

if __name__ == "__main__":
    create_key_point_images()
