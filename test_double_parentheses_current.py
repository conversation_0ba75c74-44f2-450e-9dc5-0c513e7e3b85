#!/usr/bin/env python3
"""
测试双括号"（（））"的当前显示效果
"""

import asyncio
import os
import sys

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_double_parentheses():
    """测试双括号功能的当前显示效果"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容
    test_content = """# 双括号显示效果测试

## 基本双括号测试

这是一个包含（（双括号内容））的普通段落。

这里有多个双括号：（（第一个））和（（第二个））在同一行。

## 编号列表中的双括号

1. 第一个列表项包含（（重要提示））
2. 第二个列表项有（（楷体强调））文字
3. 混合格式：**粗体**、（（橙色楷体））、*斜体*

## 答案框中的双括号

/这是答案框内容，包含（（双括号楷体文字））的示例。

答案：这里是答案内容（（重要说明））

解析：详细解析过程（（关键步骤））需要注意。/

## 复杂组合测试

这个段落包含**粗体（（楷体强调））文字**和*斜体（（楷体提示））内容*。

还有`代码文字`和（（橙色楷体））的组合。

## 长文本双括号测试

这是一个很长的段落，用来测试双括号（（这是一个比较长的双括号内容，用来测试在长文本中的显示效果））在段落中的显示效果，看看是否能正确处理换行和格式。

## 特殊字符测试

包含标点符号的双括号：（（注意！这里有感叹号））

包含数字的双括号：（（第123个要点））

包含英文的双括号：（（English text 英文混合））
"""
    
    # 配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_family="Noto Sans CJK SC",
        font_size=12,
        line_height=1.5,
        paragraph_spacing=6,
        indent_first_line=True,
        dpi=300,
        color_mode="CMYK",
        bleed=3,
        enable_hyphenation=True,
        widow_orphan_control=True
    )
    
    try:
        # 生成PDF
        pdf_path = await pdf_service.generate_pdf(
            test_content, 
            config, 
            "double_parentheses_current_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print(f"📄 文件位置: {os.path.abspath(pdf_path)}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📊 文件大小: {file_size} 字节")
            
            # 显示当前双括号处理逻辑
            print("\n🔍 当前双括号处理逻辑:")
            print("   输入: （（内容））")
            print("   输出: <font color=\"#FF8C00\" name=\"ChineseFont\">（内容）</font>")
            print("   效果: 橙色楷体文字，双括号变单括号")
            
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_double_parentheses())
