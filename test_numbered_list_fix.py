#!/usr/bin/env python3
"""
测试修复后的编号列表中双括号显示效果
"""

import asyncio
import os
import sys

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_numbered_list_fix():
    """测试修复后的编号列表中双括号显示效果"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容 - 重点测试编号列表中的双括号
    test_content = """# 编号列表双括号修复测试

## 修复前的问题
之前在编号列表中，双括号会显示为HTML标签而不是格式化的文字。

## 修复后的效果测试

### 基本编号列表双括号测试

1. 第一个列表项包含（（SimKai楷体双括号内容））
2. 第二个列表项有（（重要楷体提示））文字
3. 第三个列表项：（（完全楷体的内容））

### 混合格式测试

1. 混合格式：**粗体**、（（橙色SimKai楷体））、*斜体*
2. 代码组合：`代码文字`和（（楷体文字））
3. 多个双括号：（（第一个））和（（第二个））在同一行

### 复杂内容测试

1. 这是一个较长的列表项，包含（（长文本的楷体双括号内容，用来测试在编号列表中的换行和显示效果））
2. 包含特殊字符：（（注意！@#$%^&*()楷体符号））
3. 包含数字和英文：（（第123个要点 English text））

### 学科内容示例

1. 数学题目：计算（（重要公式））的应用
2. 语文阅读：理解（（关键词语））的含义
3. 英语语法：掌握（（语法要点））的用法

### 教学场景测试

1. 课堂重点：（（这是需要特别注意的知识点））
2. 作业要求：完成（（指定练习题））
3. 考试提醒：注意（（考试重点内容））

## 普通段落对比

这是普通段落中的（（双括号楷体内容）），应该正常显示。

## 答案框中的对比

/这是答案框内容，包含（（楷体双括号文字））的示例。

答案：这里是答案内容（（重要楷体说明））

解析：详细解析过程（（关键楷体步骤））需要注意。/

## 验证要点

如果修复成功，您应该看到：
1. 编号列表中的双括号文字显示为橙色SimKai楷体
2. 不再显示HTML标签如 `<font color="#FF8C00" name="KaiTi">`
3. 双括号变为单括号：（（内容））→（内容）
4. 与普通段落和答案框中的效果一致

## 测试结论

（（如果所有双括号都正确显示为楷体，说明修复成功！））
"""
    
    # 配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_family="Noto Sans CJK SC",
        font_size=12,
        line_height=1.5,
        paragraph_spacing=6,
        indent_first_line=True,
        dpi=300,
        color_mode="CMYK",
        bleed=3,
        enable_hyphenation=True,
        widow_orphan_control=True
    )
    
    try:
        # 生成PDF
        pdf_path = await pdf_service.generate_pdf(
            test_content, 
            config, 
            "numbered_list_fix_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print(f"📄 文件位置: {os.path.abspath(pdf_path)}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📊 文件大小: {file_size} 字节")
            
            print("\n🔧 修复说明:")
            print("   ❌ 修复前：编号列表中显示HTML标签")
            print("   ✅ 修复后：使用Paragraph对象渲染HTML格式")
            print("   🎨 效果：双括号正确显示为橙色SimKai楷体")
            print("   📐 格式：双括号变单括号，与其他位置一致")
            
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_numbered_list_fix())
