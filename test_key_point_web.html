<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重难点剖析功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        textarea {
            width: 100%;
            height: 150px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-item {
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .feature-answer {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .feature-keypoint {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .highlight {
            background-color: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 重难点剖析功能测试</h1>
        
        <div class="test-section">
            <h3>🆕 新功能介绍</h3>
            <div class="highlight">
                <h4>重难点剖析功能</h4>
                <p><strong>功能：</strong> 将文档中的"重难点剖析"文字自动替换为精美的图片标签</p>
                <p><strong>特点：</strong> 与"答案"功能完全相同的处理方式和视觉效果</p>
                <p><strong>用途：</strong> 标记教学重点、难点分析、关键概念等内容</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎨 功能对比</h3>
            <div class="feature-grid">
                <div class="feature-item feature-answer">
                    <h4>📝 答案功能</h4>
                    <p><strong>触发词：</strong> "答案"</p>
                    <p><strong>用途：</strong> 标记答案内容</p>
                    <p><strong>尺寸：</strong> 40×19.3像素</p>
                    <p><strong>位置：</strong> 突出到答案框外面</p>
                </div>
                <div class="feature-item feature-keypoint">
                    <h4>🎯 重难点剖析功能</h4>
                    <p><strong>触发词：</strong> "重难点剖析"</p>
                    <p><strong>用途：</strong> 标记重难点分析</p>
                    <p><strong>尺寸：</strong> 40×19.3像素</p>
                    <p><strong>位置：</strong> 突出到答案框外面</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试内容</h3>
            <textarea id="testContent"># 重难点剖析功能测试

## 混合使用示例

解方程：3x + 6 = 21

/
这是一个一元一次方程。

重难点剖析：解一元一次方程的关键步骤
1. 移项：将含x的项移到等号左边
2. 合并同类项
3. 系数化为1

**解题过程：**
3x + 6 = 21
3x = 21 - 6
3x = 15
x = 5

**验证：** 3 × 5 + 6 = 15 + 6 = 21 ✓

答案：x = 5

重难点剖析：验证的重要性
解完方程后一定要验证，确保答案正确。
/</textarea>
            
            <button onclick="testKeyPointFunction()">测试重难点剖析功能</button>
            <div id="testStatus"></div>
            <div id="testResult"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 检查要点</h3>
            <ul>
                <li><strong>✅ 图片替换：</strong> "重难点剖析"文字应该被替换为图片</li>
                <li><strong>✅ 尺寸一致：</strong> 重难点剖析图片与答案图片大小相同</li>
                <li><strong>✅ 位置突出：</strong> 图片应该突出到答案框外面</li>
                <li><strong>✅ 混合使用：</strong> 可以与答案图片在同一个答案框中使用</li>
                <li><strong>✅ 多个使用：</strong> 可以在同一个答案框中使用多个重难点剖析</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>📋 使用场景</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div style="padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                    <h5>📚 教育文档</h5>
                    <ul style="font-size: 14px;">
                        <li>知识点分析</li>
                        <li>解题思路</li>
                        <li>概念解释</li>
                    </ul>
                </div>
                <div style="padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                    <h5>💻 技术文档</h5>
                    <ul style="font-size: 14px;">
                        <li>核心要点</li>
                        <li>关键步骤</li>
                        <li>注意事项</li>
                    </ul>
                </div>
                <div style="padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                    <h5>📈 培训材料</h5>
                    <ul style="font-size: 14px;">
                        <li>重点内容</li>
                        <li>难点解析</li>
                        <li>关键流程</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>💡 技术实现</h3>
            <p><strong>处理流程：</strong></p>
            <ol>
                <li>文本扫描：系统扫描答案框中的"重难点剖析"文字</li>
                <li>图片替换：将文字替换为对应的图片</li>
                <li>尺寸计算：按照预设规则计算图片显示尺寸</li>
                <li>位置定位：将图片定位到答案框外面</li>
                <li>渲染输出：在PDF中绘制图片</li>
            </ol>
            
            <p><strong>图片规格：</strong></p>
            <ul>
                <li>原始尺寸：209×101像素</li>
                <li>显示尺寸：40×19.3像素</li>
                <li>背景颜色：橙色 (#f7ab00)</li>
                <li>文字颜色：白色</li>
                <li>位置：突出到答案框外面10像素</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        function showResult(content) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = `<div class="result">${content}</div>`;
        }
        
        async function testKeyPointFunction() {
            const content = document.getElementById('testContent').value.trim();
            
            if (!content) {
                showStatus('请输入测试内容', 'error');
                return;
            }
            
            showStatus('正在生成PDF测试...', 'info');
            
            const requestData = {
                content: content,
                layout_config: {
                    page_format: "A4",
                    margin_top: 2.0,
                    margin_bottom: 2.0,
                    margin_left: 3.0,  // 增加左边距以容纳突出的图片
                    margin_right: 2.0,
                    font_size: 12,
                    line_height: 1.5,
                    paragraph_spacing: 12,
                    indent_first_line: true
                },
                filename: "key_point_analysis_test.pdf"
            };
            
            try {
                const response = await fetch(`${API_BASE}/api/pdf/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showStatus('✅ PDF生成成功！', 'success');
                    
                    const downloadUrl = `${API_BASE}${result.pdf_url}`;
                    showResult(`
                        <h4>🎯 重难点剖析功能测试完成！</h4>
                        <p><strong>文件大小：</strong> ${result.file_size} 字节</p>
                        <p><strong>生成时间：</strong> ${result.generation_time.toFixed(2)} 秒</p>
                        <p><a href="${downloadUrl}" target="_blank" style="color: #007bff; text-decoration: none;">📥 点击下载PDF文件</a></p>
                        <div style="margin-top: 15px; padding: 15px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
                            <strong>🔍 检查重难点剖析功能：</strong><br>
                            1. 打开下载的PDF文件<br>
                            2. 查找答案框（圆角矩形背景）<br>
                            3. 检查"重难点剖析"是否被替换为图片<br>
                            4. 确认图片突出到答案框外面<br>
                            5. 验证与答案图片的混合使用效果<br><br>
                            <strong>✅ 如果重难点剖析图片正常显示，说明功能实现成功！</strong><br>
                            <strong>🎯 现在你有了两个强大的标记工具：答案 + 重难点剖析！</strong>
                        </div>
                    `);
                } else {
                    const errorText = await response.text();
                    showStatus('❌ PDF生成失败', 'error');
                    showResult(`<h4>错误信息：</h4><pre>${errorText}</pre>`);
                }
                
            } catch (error) {
                showStatus('❌ 网络请求失败', 'error');
                showResult(`<h4>错误详情：</h4><pre>${error.message}</pre>`);
            }
        }
    </script>
</body>
</html>
