#!/usr/bin/env python3
"""
测试简化的编号列表中双括号显示效果
"""

import asyncio
import os
import sys

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_simple_numbered_list():
    """测试简化的编号列表中双括号显示效果"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容 - 简化的双括号测试
    test_content = """# 编号列表双括号测试

## 基本双括号测试

1. 第一个列表项包含（（楷体内容））
2. 第二个列表项有（（重要提示））文字
3. 第三个列表项：（（完全楷体））

## 混合格式测试

1. 粗体和楷体：**粗体文字**（（楷体文字））
2. 斜体和楷体：*斜体文字*（（楷体文字））
3. 多个楷体：（（第一个））和（（第二个））

## 实际应用场景

1. 数学重点：掌握（（重要公式））的应用
2. 语文要点：理解（（关键词语））的含义
3. 英语语法：学习（（语法规则））的用法

## 长文本测试

1. 这是一个较长的列表项（（包含楷体强调内容））用来测试换行效果
2. 另一个长列表项（（楷体文字在长文本中的显示效果））
3. 最后一个测试项（（验证楷体格式是否正确））

## 普通段落对比

这是普通段落中的（（双括号楷体内容）），应该正常显示。

## 验证要点

如果修复成功，您应该看到：
- 编号列表中的双括号文字显示为橙色楷体
- 不再显示HTML标签
- 双括号变为单括号
- 与普通段落中的效果一致

（（修复成功的标志是所有双括号都正确显示为楷体！））
"""
    
    # 配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_family="Noto Sans CJK SC",
        font_size=12,
        line_height=1.5,
        paragraph_spacing=6,
        indent_first_line=True,
        dpi=300,
        color_mode="CMYK",
        bleed=3,
        enable_hyphenation=True,
        widow_orphan_control=True
    )
    
    try:
        # 生成PDF
        pdf_path = await pdf_service.generate_pdf(
            test_content, 
            config, 
            "simple_numbered_list_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print(f"📄 文件位置: {os.path.abspath(pdf_path)}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📊 文件大小: {file_size} 字节")
            
            print("\n🔧 修复验证:")
            print("   ✅ 使用Paragraph对象渲染HTML格式")
            print("   ✅ 添加错误处理机制")
            print("   ✅ 支持双括号楷体显示")
            print("   ✅ 与普通段落效果一致")
            
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_numbered_list())
