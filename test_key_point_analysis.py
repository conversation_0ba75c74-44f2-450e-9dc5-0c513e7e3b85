#!/usr/bin/env python3
"""
测试"重难点剖析"功能
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_key_point_analysis():
    """测试重难点剖析功能"""
    
    print("🔍 测试重难点剖析功能...")
    
    # 创建PDF服务
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# 重难点剖析功能测试

## 第一题：基础测试

计算：8 × 9 = ?

/
这是一个乘法题目。

重难点剖析：乘法运算的关键是理解重复加法的概念。

8 × 9 = 8 + 8 + 8 + 8 + 8 + 8 + 8 + 8 + 8 = 72

答案：72
/

## 第二题：混合使用测试

解方程：2x + 5 = 13

/
这是一个一元一次方程。

重难点剖析：解一元一次方程的步骤：
1. 移项：将含x的项移到等号左边，常数项移到右边
2. 合并同类项
3. 系数化为1

**解题过程：**
2x + 5 = 13
2x = 13 - 5
2x = 8
x = 4

**验证：** 2 × 4 + 5 = 8 + 5 = 13 ✓

答案：x = 4

这道题的重难点剖析帮助学生理解解方程的基本思路。
/

## 第三题：多个重难点剖析

计算三角形面积：底边 = 6cm，高 = 4cm

/
这是一个几何计算题。

重难点剖析：三角形面积公式的理解
- 公式：S = ½ × 底 × 高
- 关键点：为什么要乘以½？
- 原理：三角形是平行四边形的一半

**计算过程：**
S = ½ × 6 × 4 = ½ × 24 = 12平方厘米

重难点剖析：单位换算
- 面积单位：平方厘米（cm²）
- 注意：长度单位是厘米，面积单位是平方厘米

答案：12平方厘米

通过重难点剖析，学生能更好地理解公式的来源和应用。
/

## 第四题：复杂应用题

小明买了3支笔和2本书，笔每支5元，书每本8元，一共花了多少钱？

/
这是一个综合应用题，涉及乘法和加法运算。

重难点剖析：应用题解题步骤
1. **理解题意**：明确已知条件和求解目标
2. **分析数量关系**：找出各个量之间的关系
3. **列式计算**：根据数量关系列出算式
4. **检验答案**：验证答案是否合理

**已知条件：**
- 笔：3支，每支5元
- 书：2本，每本8元

**数量关系分析：**
- 笔的总价 = 笔的数量 × 每支笔的价格
- 书的总价 = 书的数量 × 每本书的价格
- 总花费 = 笔的总价 + 书的总价

**计算过程：**
笔的总价：3 × 5 = 15元
书的总价：2 × 8 = 16元
总花费：15 + 16 = 31元

重难点剖析：检验方法
- 估算验证：3×5≈15，2×8≈16，15+16=31，合理
- 逆向验证：31-15=16，16÷2=8，符合书的单价

答案：31元

这道题通过重难点剖析，帮助学生掌握应用题的解题方法和思维过程。
/

## 第五题：对比测试

计算：(15 + 25) ÷ 8 = ?

/
这是一个混合运算题目。

**运算顺序：**
1. 先算括号内的加法
2. 再算除法

**计算过程：**
(15 + 25) ÷ 8 = 40 ÷ 8 = 5

答案：5

这道题没有重难点剖析，可以作为对比。
/
"""
    
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=3.0,  # 保持较大左边距以容纳突出的图片
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("生成重难点剖析测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_key_point_analysis.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
                
                print("\n请检查PDF中的重难点剖析功能:")
                print("1. ✅ '重难点剖析'文字应该被替换为图片")
                print("2. ✅ 重难点剖析图片应该与答案图片大小相同")
                print("3. ✅ 重难点剖析图片应该突出到答案框外面")
                print("4. ✅ 可以与答案图片混合使用")
                print("5. ✅ 多个重难点剖析图片都正常显示")
                
                print("\n功能特点:")
                print("- 🎯 图片尺寸：40×19.3像素（与答案图片相同）")
                print("- 📍 位置：突出到答案框外面")
                print("- 🎨 样式：橙色背景，白色文字")
                print("- 🔄 处理方式：与答案图片完全相同")
                
                print("\n使用方法:")
                print("在答案框中直接使用'重难点剖析'文字，系统会自动替换为图片")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_key_point_analysis())
