#!/usr/bin/env python3
"""
调试重难点剖析功能
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def debug_key_point():
    """调试重难点剖析功能"""
    
    print("🔍 调试重难点剖析功能...")
    
    # 创建PDF服务
    pdf_service = PDFService()
    
    # 简单的测试内容
    test_content = """
# 调试测试

## 简单测试

/
重难点剖析：这是测试内容。

答案：这是答案。
/

## 只有重难点剖析

/
重难点剖析：只有重难点剖析的测试。
/

## 只有答案

/
答案：只有答案的测试。
/
"""
    
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=3.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("生成调试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="debug_key_point.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_key_point())
