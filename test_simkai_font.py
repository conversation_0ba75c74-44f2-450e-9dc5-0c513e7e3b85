#!/usr/bin/env python3
"""
测试SimKai楷体字体注册和双括号显示效果
"""

import asyncio
import os
import sys

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_simkai_font():
    """测试SimKai楷体字体的双括号显示效果"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容 - 重点测试双括号楷体效果
    test_content = """# SimKai楷体字体测试

## 双括号楷体文字测试

这是一个包含（（SimKai楷体双括号内容））的普通段落。

多个双括号测试：（（第一个楷体））和（（第二个楷体））在同一行。

## 编号列表中的楷体双括号

1. 第一个列表项包含（（重要楷体提示））
2. 第二个列表项有（（SimKai楷体强调））文字
3. 混合格式：**粗体**、（（橙色SimKai楷体））、*斜体*

## 答案框中的楷体双括号

/这是答案框内容，包含（（SimKai楷体双括号文字））的示例。

答案：这里是答案内容（（重要楷体说明））

解析：详细解析过程（（关键楷体步骤））需要注意。/

## 楷体字体特色测试

（（楷体具有优美的笔画和传统的书法韵味））

（（SimKai字体应该显示为正宗的楷体风格））

（（测试中文标点符号：，。！？；：""''））

## 长文本楷体测试

这是一个很长的段落，用来测试双括号（（这是一个比较长的SimKai楷体双括号内容，用来测试楷体字在长文本中的显示效果和换行处理能力））在段落中的显示效果。

## 特殊字符楷体测试

包含数字的楷体：（（第123个楷体要点））

包含英文的楷体：（（English text 英文楷体混合））

包含特殊符号的楷体：（（注意！@#$%^&*()楷体符号））

## 楷体与其他格式组合

这个段落包含**粗体（（楷体强调））文字**和*斜体（（楷体提示））内容*。

还有`代码文字`和（（橙色SimKai楷体））的组合。

## 楷体字体验证

如果您看到双括号中的文字显示为楷体风格，说明SimKai字体注册成功！

（（楷体应该具有传统书法的优雅笔画））

（（与普通字体相比，楷体更加端正和规范））
"""
    
    # 配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_family="Noto Sans CJK SC",
        font_size=12,
        line_height=1.5,
        paragraph_spacing=6,
        indent_first_line=True,
        dpi=300,
        color_mode="CMYK",
        bleed=3,
        enable_hyphenation=True,
        widow_orphan_control=True
    )
    
    try:
        # 生成PDF
        pdf_path = await pdf_service.generate_pdf(
            test_content, 
            config, 
            "simkai_font_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print(f"📄 文件位置: {os.path.abspath(pdf_path)}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📊 文件大小: {file_size} 字节")
            
            # 显示SimKai字体信息
            print("\n🔍 SimKai楷体字体信息:")
            print("   字体路径: /Users/<USER>/Library/Fonts/simkai.ttf")
            print("   字体名称: SimKai")
            print("   注册名称: KaiTi")
            print("   双括号效果: 橙色SimKai楷体文字")
            
            # 检查字体文件是否存在
            simkai_path = "/Users/<USER>/Library/Fonts/simkai.ttf"
            if os.path.exists(simkai_path):
                print(f"   ✅ SimKai字体文件存在: {simkai_path}")
            else:
                print(f"   ❌ SimKai字体文件不存在: {simkai_path}")
            
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simkai_font())
