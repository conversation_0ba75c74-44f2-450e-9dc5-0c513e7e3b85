#!/usr/bin/env python3
"""
测试字体对比 - 验证SimKai楷体与普通字体的区别
"""

import asyncio
import os
import sys

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_font_comparison():
    """测试字体对比效果"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容 - 对比不同字体效果
    test_content = """# 字体对比测试

## 双括号楷体 vs 普通文字对比

### 普通文字示例
这是普通的中文文字，使用系统默认字体显示。

### 双括号楷体文字示例
这是包含（（SimKai楷体双括号文字））的段落，双括号内应该显示为楷体。

### 详细对比

1. 普通文字：这是普通字体的文字效果
2. 楷体文字：（（这是SimKai楷体的文字效果））
3. 混合效果：普通文字和（（楷体文字））的混合显示

### 字体特征对比

**普通字体特征：**
- 笔画相对简洁
- 字形较为现代化
- 适合正文阅读

**楷体字体特征：**
（（笔画优美，具有书法韵味））
（（字形端正，传统典雅））
（（适合强调和装饰性文字））

### 实际应用场景

在文档中，我们可以使用（（重要提示））来突出关键信息。

答案框中的楷体效果：

/这是答案框内容。

答案：（（这里是楷体答案））

解析：普通文字解析，（（重点楷体强调））部分。/

### 编号列表中的对比

1. 第一项：普通文字内容（（楷体强调内容））
2. 第二项：（（完全楷体的列表项内容））
3. 第三项：混合**粗体**、（（楷体））、*斜体*格式

### 长文本测试

这是一个较长的段落，用来测试（（SimKai楷体在长文本中的显示效果，包括换行处理、字间距调整、以及与普通文字的协调性））的实际表现。

### 特殊字符测试

（（包含数字：123456789））
（（包含英文：ABC abc XYZ xyz））
（（包含符号：！@#￥%……&*（）））
（（包含标点：，。；：""''？））

## 总结

如果您能明显看出双括号内文字与普通文字的字体差异，说明SimKai楷体注册成功！

（（楷体应该更加优雅和传统））
（（与普通字体形成鲜明对比））
"""
    
    # 配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_family="Noto Sans CJK SC",
        font_size=12,
        line_height=1.5,
        paragraph_spacing=6,
        indent_first_line=True,
        dpi=300,
        color_mode="CMYK",
        bleed=3,
        enable_hyphenation=True,
        widow_orphan_control=True
    )
    
    try:
        # 生成PDF
        pdf_path = await pdf_service.generate_pdf(
            test_content, 
            config, 
            "font_comparison_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print(f"📄 文件位置: {os.path.abspath(pdf_path)}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📊 文件大小: {file_size} 字节")
            
            print("\n🔍 字体对比说明:")
            print("   📝 普通文字：使用系统默认中文字体")
            print("   ✨ 双括号文字：使用SimKai楷体字体")
            print("   🎨 颜色：双括号文字为橙色 (#FF8C00)")
            print("   📐 格式：双括号变单括号 （（内容）） → （内容）")
            
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_font_comparison())
