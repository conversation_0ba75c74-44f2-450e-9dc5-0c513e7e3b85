#!/usr/bin/env python3
"""
测试文件上传API的脚本
"""

import requests
import json

def test_upload():
    """测试文件上传功能"""
    
    # API端点
    url = "http://localhost:8001/api/documents/upload"
    
    # 测试文件
    test_file_path = "test_upload.md"
    
    try:
        # 读取测试文件
        with open(test_file_path, 'rb') as f:
            files = {'file': (test_file_path, f, 'text/markdown')}
            
            # 发送上传请求
            print(f"正在上传文件: {test_file_path}")
            response = requests.post(url, files=files)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {response.headers}")
            
            if response.status_code == 200:
                result = response.json()
                print("上传成功!")
                print(f"文件ID: {result.get('file_id')}")
                print(f"文件名: {result.get('filename')}")
                print(f"文件类型: {result.get('file_type')}")
                print(f"文件大小: {result.get('file_size')} bytes")
                print(f"消息: {result.get('message')}")
                print(f"Markdown内容长度: {len(result.get('markdown_content', ''))}")
                print(f"Markdown内容预览: {result.get('markdown_content', '')[:200]}...")
            else:
                print("上传失败!")
                print(f"错误信息: {response.text}")
                
    except FileNotFoundError:
        print(f"测试文件 {test_file_path} 不存在")
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_upload()
