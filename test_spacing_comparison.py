#!/usr/bin/env python3
"""
测试不同间距效果对比
"""

import asyncio
import os
import sys

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_spacing_comparison():
    """测试不同间距效果对比"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容 - 简洁的对比测试
    test_content = """# 序号与文字间距效果测试

## 当前间距设置

序号圆形右侧8px开始文字，总体更紧凑。

## 基本测试

1. 第一个测试项目
2. 第二个测试项目包含（（楷体内容））
3. 第三个测试项目：**粗体**和*斜体*
4. 第四个测试项目有较长的文字内容用来测试换行效果
5. 第五个测试项目（（楷体强调文字））在列表中的显示

## 实际内容测试

1. 数学题目：计算（（重要公式））
2. 语文阅读：理解（（关键词语））
3. 英语语法：掌握（（语法要点））
4. 科学实验：观察（（实验现象））
5. 历史学习：记住（（重要事件））

## 长文本测试

1. 这是一个比较长的列表项，用来测试当文字内容较多时，序号与文字的间距是否合适，以及整体的视觉效果
2. 另一个长文本项目（（包含楷体强调内容））用来验证在复杂格式下的显示效果
3. 最后一个测试项目包含多种格式：**粗体**、（（楷体））、*斜体*、`代码`的混合使用

## 两位数序号测试

8. 第八项
9. 第九项  
10. 第十项（两位数测试）
11. 第十一项
12. 第十二项

## 验证要点

如果间距调整合适，应该看到：
- 序号与文字距离适中
- 整体排版紧凑美观
- 不影响阅读体验
- 双括号楷体正常显示

（（间距调整完成，排版效果更佳！））
"""
    
    # 配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_family="Noto Sans CJK SC",
        font_size=12,
        line_height=1.5,
        paragraph_spacing=6,
        indent_first_line=True,
        dpi=300,
        color_mode="CMYK",
        bleed=3,
        enable_hyphenation=True,
        widow_orphan_control=True
    )
    
    try:
        # 生成PDF
        pdf_path = await pdf_service.generate_pdf(
            test_content, 
            config, 
            "spacing_comparison_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print(f"📄 文件位置: {os.path.abspath(pdf_path)}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📊 文件大小: {file_size} 字节")
            
            print("\n📏 当前间距设置:")
            print("   🔵 圆形直径：20px")
            print("   📍 圆形中心：距左边10px")
            print("   📝 文字开始：圆形右侧8px（总计28px）")
            print("   📐 相比之前：减少7px间距")
            
            print("\n🎯 预期效果:")
            print("   ✅ 更紧凑的排版")
            print("   ✅ 更好的视觉平衡")
            print("   ✅ 保持良好可读性")
            print("   ✅ 双括号楷体正常显示")
            
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_spacing_comparison())
