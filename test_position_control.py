#!/usr/bin/env python3
"""
测试图片位置控制功能的脚本
"""

import requests
import json

def test_position_control():
    """测试图片位置控制功能"""
    
    # API端点
    url = "http://localhost:8001/api/pdf/generate"
    
    # 测试内容 - 包含各种位置控制参数
    test_content = """# 图片位置控制测试

## 基本对齐测试

### 左对齐
![左对齐图片](test_images/calculation_steps.png?size=small&align=left)

### 居中对齐
![居中图片](test_images/math_diagram.png?size=small&align=center)

### 右对齐
![右对齐图片](test_images/rectangle.png?size=small&align=right)

## 边距控制测试

### 左边距
![左边距图片](test_images/calculation_steps.png?size=small&margin_left=50)

### 右边距
![右边距图片](test_images/math_diagram.png?size=small&margin_right=50)

### 上下边距
![上下边距图片](test_images/rectangle.png?size=small&margin_top=20&margin_bottom=20)

## 偏移控制测试

### 水平偏移
![水平偏移图片](test_images/calculation_steps.png?size=small&offset_x=30)

### 垂直偏移
![垂直偏移图片](test_images/math_diagram.png?size=small&offset_y=15)

### 组合偏移
![组合偏移图片](test_images/rectangle.png?size=small&offset_x=20&offset_y=10)

## 复杂组合测试

### 左对齐 + 右边距 + 垂直偏移
![复杂控制1](test_images/calculation_steps.png?size=medium&align=left&margin_right=30&offset_y=5)

### 右对齐 + 左边距 + 水平偏移
![复杂控制2](test_images/math_diagram.png?size=medium&align=right&margin_left=30&offset_x=-10)

## 图片与文字混合测试

![第一张图](test_images/calculation_steps.png?size=small&margin_bottom=10)

这是穿插在两张图片之间的文字内容，用于测试图文混排效果。

![第二张图](test_images/math_diagram.png?size=small&margin_top=10)

测试完成！
"""
    
    try:
        # 准备请求数据
        request_data = {
            "content": test_content,
            "layout_config": {
                "page_format": "A4",
                "margin_top": 2.0,
                "margin_bottom": 2.0,
                "margin_left": 2.0,
                "margin_right": 2.0,
                "font_family": "Noto Sans CJK SC",
                "font_size": 12,
                "line_height": 1.5,
                "paragraph_spacing": 6,
                "indent_first_line": True,
                "dpi": 300,
                "color_mode": "CMYK",
                "bleed": 3,
                "enable_hyphenation": True,
                "widow_orphan_control": True
            },
            "filename": "position_control_test.pdf"
        }

        # 发送PDF生成请求
        print("正在生成PDF...")
        response = requests.post(url, json=request_data)

        print(f"响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("PDF生成成功!")
            print(f"PDF URL: {result.get('pdf_url')}")
            print(f"页数: {result.get('page_count')}")
            print(f"文件大小: {result.get('file_size')} bytes")
            print(f"生成时间: {result.get('generation_time'):.2f} 秒")
            print(f"消息: {result.get('message')}")

            # 下载PDF文件
            if result.get('pdf_url'):
                download_url = f"http://localhost:8001{result['pdf_url']}"
                print(f"下载URL: {download_url}")

                download_response = requests.get(download_url)
                if download_response.status_code == 200:
                    with open("position_control_test.pdf", 'wb') as f:
                        f.write(download_response.content)
                    print("PDF已下载并保存为: position_control_test.pdf")
                else:
                    print(f"下载失败: {download_response.status_code}")

        else:
            print("PDF生成失败!")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_position_control()
